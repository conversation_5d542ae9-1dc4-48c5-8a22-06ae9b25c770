<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <template id="xtream_theme_snippets" inherit_id="website.snippets"
              name="About Us">
        <!-- The template defines various snippets of content that will be displayed on the page, such as a amazing, arrival demo,
        discount, main, main product, map, new arrivals, and testimonial. The template uses XPath expressions to position the snippets within the page
        structure. The "inherit_id" attribute indicates that the template inherits from the "website.snippets" template. -->
        <xpath expr="//snippets[@id='snippet_groups']" position="inside">
            <t t-snippet="theme_xtream.s_amazing"
               t-thumbnail="/theme_xtream/static/src/img/snippets/amazing_banner.jpg"/>
            <t t-snippet="theme_xtream.s_discount"
               t-thumbnail="/theme_xtream/static/src/img/snippets/discount.jpg"/>
            <t t-snippet="theme_xtream.s_main"
               t-thumbnail="/theme_xtream/static/src/img/snippets/main_banner.jpg"/>
            <t t-snippet="theme_xtream.s_main_product"
               t-thumbnail="/theme_xtream/static/src/img/snippets/main_product.jpg"/>
            <t t-snippet="theme_xtream.s_services"
               t-thumbnail="/theme_xtream/static/src/img/snippets/services.jpg"/>
        </xpath>
        <xpath expr="//snippets[@id='snippet_content']//t[@t-snippet][last()]" position="after">
            <t t-snippet="theme_xtream.s_new_arrivals"
               t-thumbnail="/theme_xtream/static/src/img/snippets/new_arrivals.jpg"/>
            <t t-snippet="theme_xtream.s_testimonial"
               t-thumbnail="/theme_xtream/static/src/img/snippets/testimonial.jpg"/>
        </xpath>
    </template>
</odoo>
